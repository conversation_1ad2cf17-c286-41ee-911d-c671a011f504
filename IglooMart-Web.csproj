﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>IglooMart_Web</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Views\Address\Edit.cshtml" />
    <Content Remove="wwwroot\images\team\team-01.png" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Views\Address\Edit.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <!--<PackageReference Include="iTextSharp" Version="********" />-->
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="Syncfusion.EJ2.AspNet.Core" Version="27.1.55" />
	  <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.14" />

	  <PackageReference Include="System.Linq" Version="4.3.0" />
    <PackageReference Include="ZXing.Net" Version="0.16.10" />
  </ItemGroup>

  <ItemGroup>
    <UpToDateCheckInput Remove="Views\Address\Edit.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="Views\Address\Edit.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\css\rtl\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\Admin\images\favicon.ico">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
