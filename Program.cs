

//using IglooMart_Web.Services.Authorization;

using IglooMart_Web.Services.Authorization;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;

using IglooMart_Web.Services;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Net;
using System.Text;
using IglooMart_Web.Services.Helper;
using IglooMart_Web;
using IglooMart_Web.Services._Base;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews().AddRazorRuntimeCompilation();

builder.Services.AddHttpClient("Pal4it", config =>
{
    var url = builder.Configuration["BaseUrl"];
    config.BaseAddress = new Uri(url ?? "");
});
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Auth/Login";
    options.AccessDeniedPath = "/Home";
});
builder.Services.AddMyLocalizations();
builder.Services.AddHttpContextAccessor();
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSignalR();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromDays(365); // Set session timeout
    //options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.HttpOnly = true;
});

builder.Services.AddControllersWithViews()
    .AddNewtonsoftJson(options =>
        options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore);
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultSignInScheme = CookieAuthenticationDefaults.AuthenticationScheme;
})
.AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
    {
        ValidateIssuerSigningKey = false,
        ValidateIssuer = false,
        ValidateAudience = false,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
})
.AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
{
    options.LoginPath = "/Account/SignIn";  // Redirect to login when unauthorized
    options.AccessDeniedPath = "/Account/AccessDenied"; // Redirect when forbidden
    options.ExpireTimeSpan = TimeSpan.FromDays(365);
    options.SlidingExpiration = true;
});






builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddScoped<IHttpService, HttpService>();
builder.Services.AddScoped<CatalogService>();

builder.Services.AddScoped<SliderService>();
builder.Services.AddScoped<OfferService>();
builder.Services.AddScoped<CategoryService>();
builder.Services.AddScoped<CartService>();
builder.Services.AddScoped<ProductService>();
builder.Services.AddScoped<AddressService>();
builder.Services.AddScoped<SettingService>();
builder.Services.AddScoped<ChatService>();
builder.Services.AddScoped<ERP_UserNameService>();

builder.Services.AddScoped<AttributeService>();
builder.Services.AddScoped<ProductGroupsService>();
builder.Services.AddScoped<LookupsService>();
builder.Services.AddScoped<WishlistService>();
builder.Services.AddScoped<AboutService>();
builder.Services.AddScoped<CouponService>();

builder.Services.AddScoped<ContactMessagesService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<TokenService>();

builder.Services.AddScoped<ReviewService>();
builder.Services.AddScoped<PurchaseService>();

builder.Services.AddScoped<WebWorkContext>();

builder.Services.AddScoped<CustomerClassesServise>();
builder.Services.AddScoped<OrderService>();
builder.Services.AddScoped<NotificationService>();
builder.Services.AddScoped<ItemTransactionService>();
builder.Services.AddScoped(typeof(CacheService<>));
builder.Services.AddScoped<IglooMart_Web.Services.LanguageService>();
builder.Services.AddScoped<StatisticsService>();
builder.Services.AddMemoryCache();



Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mgo+DSMBMAY9C3t2UlhhQlVMfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTX9TdEZjW3pbcHBcQGRa");

var app = builder.Build();
app.UseSession();
// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseStatusCodePagesWithReExecute("/Home/NotFound");
    app.UseStatusCodePagesWithReExecute("/Home/BadRequest");
    app.UseExceptionHandler("/Home/InternalServerError");
    app.UseStatusCodePagesWithReExecute("/Home/NoContent");


    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();
app.UseAuthentication(); 
app.UseAuthorization();

//app.Use(async (context, next) =>
//{
//    await next();

//    if (context.Response.StatusCode == (int)HttpStatusCode.NotFound)
//    {
//        context.Response.Redirect("/Home/NotFound");
//    }
//});


//app.Use(async (context, next) =>
//{
//    await next();

//    if (context.Response.StatusCode == (int)HttpStatusCode.BadRequest)
//    {
//        context.Response.Redirect("/Home/BadRequest");
//    }
//});


//app.Use(async (context, next) =>
//{
//    try
//    {
//        await next();
//    }
//    catch (Exception)
//    {
//        context.Response.Redirect("/Home/InternalServerError");
//    }
//});


//app.Use(async (context, next) =>
//{
//    await next();

//    if (context.Response.StatusCode == (int)HttpStatusCode.NoContent)
//    {
//        context.Response.Redirect("/Home/NoContent");
//    }
//});


//app.Use(async (context, next) =>
//{
//    await next();

//    if (context.    Response.StatusCode == (int)HttpStatusCode.Unauthorized)
//    {
//        context.Response.Redirect("/Account/Signin");
//    }
//});
var supportedCultures = new[] { "en", "ar" };
var localizationOptions = new RequestLocalizationOptions().SetDefaultCulture(supportedCultures[1]);

app.UseRequestLocalization(localizationOptions);


app.MapAreaControllerRoute(
    name: "AdminArea",
    areaName: "Admin",
    pattern: "Admin/{controller=Dashboard}/{action=DashBoard}/{id?}");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");


app.Run();
